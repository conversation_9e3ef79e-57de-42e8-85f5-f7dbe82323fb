const mongoose = require('mongoose');

const emailSchema = new mongoose.Schema({
  // Basic email information
  subject: {
    type: String,
    required: true,
    trim: true
  },
  content: {
    type: String,
    required: true
  },
  htmlContent: {
    type: String
  },
  
  // Sender information
  sender: {
    type: String,
    required: true,
    trim: true
  },
  senderEmail: {
    type: String,
    required: true,
    lowercase: true,
    trim: true
  },
  
  // Recipient information
  recipients: [{
    email: {
      type: String,
      required: true,
      lowercase: true
    },
    name: String,
    type: {
      type: String,
      enum: ['to', 'cc', 'bcc'],
      default: 'to'
    }
  }],
  
  // Email metadata
  messageId: {
    type: String,
    unique: true,
    required: true
  },
  threadId: String,
  inReplyTo: String,
  references: [String],
  
  // Timestamps
  receivedAt: {
    type: Date,
    default: Date.now
  },
  sentAt: Date,
  
  // Email status
  read: {
    type: Boolean,
    default: false
  },
  archived: {
    type: Boolean,
    default: false
  },
  deleted: {
    type: Boolean,
    default: false
  },
  blocked: {
    type: Boolean,
    default: false
  },
  unsubscribed: {
    type: Boolean,
    default: false
  },
  
  // Inbox Zero methodology fields
  category: {
    type: String,
    enum: ['customer', 'newsletter', 'cold', 'internal', 'urgent', 'service', 'quote', 'invoice'],
    index: true
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium'
  },
  action: {
    type: String,
    enum: ['deleted', 'delegated', 'deferred', 'completed', 'unsubscribed', 'blocked', 'categorized', 'archived']
  },
  
  // 4 D's framework fields
  assignedTo: {
    type: String // User ID or email of person delegated to
  },
  deferredUntil: Date,
  actionResult: mongoose.Schema.Types.Mixed,
  
  // Processing timestamps
  processedAt: Date,
  categorizedAt: Date,
  archivedAt: Date,
  deletedAt: Date,
  blockedAt: Date,
  unsubscribedAt: Date,
  
  // Attachments
  attachments: [{
    filename: String,
    contentType: String,
    size: Number,
    url: String,
    isM4A: {
      type: Boolean,
      default: false
    },
    transcribed: {
      type: Boolean,
      default: false
    },
    transcriptionText: String,
    transcriptionConfidence: Number
  }],
  
  // AI analysis results
  aiAnalysis: {
    sentiment: {
      type: String,
      enum: ['positive', 'negative', 'neutral']
    },
    sentimentScore: Number,
    intent: String,
    entities: [String],
    keywords: [String],
    isServiceRequest: Boolean,
    isQuoteRequest: Boolean,
    urgencyLevel: Number,
    customerSatisfaction: Number
  },
  
  // HVAC-specific fields
  hvacData: {
    isServiceRelated: Boolean,
    equipmentMentioned: [String],
    serviceType: {
      type: String,
      enum: ['installation', 'maintenance', 'repair', 'inspection', 'quote']
    },
    location: String,
    estimatedValue: Number,
    customerType: {
      type: String,
      enum: ['residential', 'commercial', 'industrial']
    }
  },
  
  // Email source and integration
  source: {
    type: String,
    enum: ['gmail', 'outlook', 'imap', 'pop3', 'webhook'],
    default: 'imap'
  },
  sourceAccountId: String,
  
  // Automation and rules
  automationRules: [{
    ruleId: String,
    ruleName: String,
    appliedAt: Date,
    result: String
  }],
  
  // Links and references
  unsubscribeLink: String,
  trackingPixels: [String],
  externalLinks: [String],
  
  // CRM integration
  relatedClient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Client'
  },
  relatedOpportunity: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Opportunity'
  },
  relatedServiceTicket: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ServiceTicket'
  },
  relatedQuote: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Quote'
  },
  
  // Email thread management
  isThread: {
    type: Boolean,
    default: false
  },
  threadEmails: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Email'
  }],
  
  // Security and spam detection
  spamScore: Number,
  isSpam: {
    type: Boolean,
    default: false
  },
  securityFlags: [String],
  
  // Performance metrics
  processingTime: Number, // milliseconds
  aiProcessingTime: Number, // milliseconds
  
  // Audit trail
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin'
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin'
  },
  
  // Standard fields
  enabled: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
emailSchema.index({ senderEmail: 1 });
emailSchema.index({ receivedAt: -1 });
emailSchema.index({ category: 1 });
emailSchema.index({ read: 1 });
emailSchema.index({ archived: 1 });
emailSchema.index({ deleted: 1 });
emailSchema.index({ processedAt: -1 });
emailSchema.index({ messageId: 1 }, { unique: true });
emailSchema.index({ threadId: 1 });

// Compound indexes
emailSchema.index({ senderEmail: 1, receivedAt: -1 });
emailSchema.index({ category: 1, receivedAt: -1 });
emailSchema.index({ read: 1, archived: 1, deleted: 1 });

// Virtual for email age
emailSchema.virtual('ageInHours').get(function() {
  return Math.floor((Date.now() - this.receivedAt) / (1000 * 60 * 60));
});

// Virtual for processing status
emailSchema.virtual('isProcessed').get(function() {
  return !!this.processedAt;
});

// Virtual for inbox status (for Inbox Zero)
emailSchema.virtual('inInbox').get(function() {
  return !this.archived && !this.deleted && !this.blocked;
});

// Methods
emailSchema.methods.markAsRead = function() {
  this.read = true;
  return this.save();
};

emailSchema.methods.categorize = function(category) {
  this.category = category;
  this.categorizedAt = new Date();
  return this.save();
};

emailSchema.methods.applyAction = function(action, data = {}) {
  this.action = action;
  this.processedAt = new Date();
  
  switch (action) {
    case 'deleted':
      this.deleted = true;
      this.deletedAt = new Date();
      break;
    case 'archived':
      this.archived = true;
      this.archivedAt = new Date();
      break;
    case 'delegated':
      this.assignedTo = data.assignee;
      break;
    case 'deferred':
      this.deferredUntil = data.deferUntil;
      break;
    case 'blocked':
      this.blocked = true;
      this.blockedAt = new Date();
      break;
    case 'unsubscribed':
      this.unsubscribed = true;
      this.unsubscribedAt = new Date();
      break;
  }
  
  return this.save();
};

// Static methods
emailSchema.statics.getInboxCount = function() {
  return this.countDocuments({
    archived: { $ne: true },
    deleted: { $ne: true },
    blocked: { $ne: true }
  });
};

emailSchema.statics.getUnreadCount = function() {
  return this.countDocuments({
    read: false,
    archived: { $ne: true },
    deleted: { $ne: true }
  });
};

emailSchema.statics.getCategoryStats = function() {
  return this.aggregate([
    {
      $match: {
        archived: { $ne: true },
        deleted: { $ne: true }
      }
    },
    {
      $group: {
        _id: '$category',
        count: { $sum: 1 }
      }
    },
    {
      $sort: { count: -1 }
    }
  ]);
};

module.exports = mongoose.model('Email', emailSchema);
