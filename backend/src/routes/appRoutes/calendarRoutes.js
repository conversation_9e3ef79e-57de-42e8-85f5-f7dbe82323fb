/**
 * CALENDAR ROUTES
 * API routes for enhanced calendar integration with 3-category HVAC system
 * Categories: 🔧 <PERSON><PERSON><PERSON>, 🏗️ Instalacja, 🔍 Oględziny
 */

const express = require('express');
const { body, param, query } = require('express-validator');

// Import controller
const calendarController = require('@/controllers/appControllers/calendarController');

// Import middleware
const { requireAuth } = require('@/middleware/auth');
const { requirePermission } = require('@/middleware/permissions');

const router = express.Router();

/**
 * MAIN CALENDAR ROUTES
 */

// GET /api/calendar/events
// Get calendar events with filtering and optimization
router.get(
  '/events',
  requireAuth,
  requirePermission('calendar:view'),
  [
    query('startDate')
      .isISO8601()
      .withMessage('Start date must be in ISO8601 format'),
    query('endDate')
      .isISO8601()
      .withMessage('End date must be in ISO8601 format'),
    query('category')
      .optional()
      .isIn(['all', 'serwis', 'instalacja', 'oględziny'])
      .withMessage('Invalid HVAC category'),
    query('district')
      .optional()
      .isString()
      .withMessage('District must be a string'),
    query('technician')
      .optional()
      .custom((value) => value === 'all' || value.match(/^[0-9a-fA-F]{24}$/))
      .withMessage('Invalid technician ID'),
    query('status')
      .optional()
      .isIn(['all', 'scheduled', 'in_progress', 'completed', 'cancelled'])
      .withMessage('Invalid status'),
    query('optimizeRoutes')
      .optional()
      .isBoolean()
      .withMessage('Optimize routes must be boolean'),
    query('optimizeDate')
      .optional()
      .isISO8601()
      .withMessage('Optimize date must be in ISO8601 format')
  ],
  calendarController.getCalendarEvents
);

// POST /api/calendar/events
// Create new calendar event
router.post(
  '/events',
  requireAuth,
  requirePermission('calendar:create'),
  [
    body('title')
      .notEmpty()
      .isLength({ min: 3, max: 100 })
      .withMessage('Title must be between 3 and 100 characters'),
    body('description')
      .optional()
      .isLength({ max: 500 })
      .withMessage('Description must be max 500 characters'),
    body('category')
      .isIn(['serwis', 'instalacja', 'oględziny'])
      .withMessage('Invalid HVAC category'),
    body('clientId')
      .isMongoId()
      .withMessage('Invalid client ID'),
    body('equipmentId')
      .optional()
      .isMongoId()
      .withMessage('Invalid equipment ID'),
    body('scheduledDate')
      .isISO8601()
      .withMessage('Scheduled date must be in ISO8601 format'),
    body('duration')
      .optional()
      .isInt({ min: 30, max: 480 })
      .withMessage('Duration must be between 30 and 480 minutes'),
    body('address')
      .notEmpty()
      .withMessage('Address is required'),
    body('district')
      .notEmpty()
      .withMessage('District is required'),
    body('technicianId')
      .optional()
      .isMongoId()
      .withMessage('Invalid technician ID'),
    body('priority')
      .optional()
      .isIn(['low', 'medium', 'high', 'urgent'])
      .withMessage('Invalid priority level')
  ],
  calendarController.createCalendarEvent
);

// PUT /api/calendar/events/:eventId
// Update calendar event
router.put(
  '/events/:eventId',
  requireAuth,
  requirePermission('calendar:edit'),
  [
    param('eventId').isMongoId().withMessage('Invalid event ID'),
    body('title')
      .optional()
      .isLength({ min: 3, max: 100 })
      .withMessage('Title must be between 3 and 100 characters'),
    body('description')
      .optional()
      .isLength({ max: 500 })
      .withMessage('Description must be max 500 characters'),
    body('scheduledDate')
      .optional()
      .isISO8601()
      .withMessage('Scheduled date must be in ISO8601 format'),
    body('duration')
      .optional()
      .isInt({ min: 30, max: 480 })
      .withMessage('Duration must be between 30 and 480 minutes'),
    body('technicianId')
      .optional()
      .isMongoId()
      .withMessage('Invalid technician ID'),
    body('status')
      .optional()
      .isIn(['scheduled', 'in_progress', 'completed', 'cancelled'])
      .withMessage('Invalid status'),
    body('priority')
      .optional()
      .isIn(['low', 'medium', 'high', 'urgent'])
      .withMessage('Invalid priority level')
  ],
  calendarController.updateCalendarEvent
);

// DELETE /api/calendar/events/:eventId
// Delete calendar event
router.delete(
  '/events/:eventId',
  requireAuth,
  requirePermission('calendar:delete'),
  [
    param('eventId').isMongoId().withMessage('Invalid event ID')
  ],
  calendarController.deleteCalendarEvent
);

/**
 * CALENDAR ANALYTICS AND OPTIMIZATION ROUTES
 */

// GET /api/calendar/dashboard
// Get calendar dashboard data
router.get(
  '/dashboard',
  requireAuth,
  requirePermission('calendar:view'),
  [
    query('startDate')
      .optional()
      .isISO8601()
      .withMessage('Start date must be in ISO8601 format'),
    query('endDate')
      .optional()
      .isISO8601()
      .withMessage('End date must be in ISO8601 format')
  ],
  calendarController.getCalendarDashboard
);

// GET /api/calendar/optimize/warsaw-routes
// Get Warsaw route optimization
router.get(
  '/optimize/warsaw-routes',
  requireAuth,
  requirePermission('calendar:optimize'),
  [
    query('date')
      .isISO8601()
      .withMessage('Date must be in ISO8601 format')
  ],
  calendarController.getWarsawRouteOptimization
);

// GET /api/calendar/availability/technician
// Get technician availability
router.get(
  '/availability/technician',
  requireAuth,
  requirePermission('calendar:view'),
  [
    query('technicianId')
      .isMongoId()
      .withMessage('Invalid technician ID'),
    query('date')
      .isISO8601()
      .withMessage('Date must be in ISO8601 format')
  ],
  calendarController.getTechnicianAvailability
);

// GET /api/calendar/suggest/time-slots
// Suggest optimal time slots
router.get(
  '/suggest/time-slots',
  requireAuth,
  requirePermission('calendar:view'),
  [
    query('district')
      .notEmpty()
      .withMessage('District is required'),
    query('category')
      .isIn(['serwis', 'instalacja', 'oględziny'])
      .withMessage('Invalid HVAC category'),
    query('preferredDate')
      .isISO8601()
      .withMessage('Preferred date must be in ISO8601 format')
  ],
  calendarController.suggestOptimalTimeSlots
);

/**
 * CONFIGURATION ROUTES
 */

// GET /api/calendar/config/hvac-categories
// Get HVAC categories configuration
router.get(
  '/config/hvac-categories',
  requireAuth,
  requirePermission('calendar:view'),
  calendarController.getHVACCategories
);

// GET /api/calendar/config/warsaw-districts
// Get Warsaw districts configuration
router.get(
  '/config/warsaw-districts',
  requireAuth,
  requirePermission('calendar:view'),
  calendarController.getWarsawDistricts
);

/**
 * STATISTICS ROUTES
 */

// GET /api/calendar/stats/summary
// Get calendar statistics summary
router.get(
  '/stats/summary',
  requireAuth,
  requirePermission('calendar:view'),
  async (req, res) => {
    try {
      const moment = require('moment');
      const ServiceOrder = require('@/models/appModels/ServiceOrder');

      const now = moment();
      const startOfMonth = now.clone().startOf('month').toDate();
      const endOfMonth = now.clone().endOf('month').toDate();
      const startOfWeek = now.clone().startOf('week').toDate();
      const endOfWeek = now.clone().endOf('week').toDate();

      const [
        totalThisMonth,
        totalThisWeek,
        totalToday,
        upcomingEvents,
        categoryStats
      ] = await Promise.all([
        ServiceOrder.countDocuments({
          scheduledDate: { $gte: startOfMonth, $lte: endOfMonth }
        }),
        ServiceOrder.countDocuments({
          scheduledDate: { $gte: startOfWeek, $lte: endOfWeek }
        }),
        ServiceOrder.countDocuments({
          scheduledDate: {
            $gte: now.clone().startOf('day').toDate(),
            $lte: now.clone().endOf('day').toDate()
          }
        }),
        ServiceOrder.countDocuments({
          scheduledDate: { $gte: now.toDate() },
          status: 'scheduled'
        }),
        ServiceOrder.aggregate([
          {
            $match: {
              scheduledDate: { $gte: startOfMonth, $lte: endOfMonth }
            }
          },
          {
            $group: {
              _id: '$category',
              count: { $sum: 1 }
            }
          }
        ])
      ]);

      const summary = {
        totalThisMonth,
        totalThisWeek,
        totalToday,
        upcomingEvents,
        categoryBreakdown: categoryStats.reduce((acc, stat) => {
          acc[stat._id] = stat.count;
          return acc;
        }, {}),
        efficiency: totalThisWeek > 0 ? Math.round((totalThisWeek / 35) * 100) : 0 // Assuming 35 slots per week
      };

      res.status(200).json({
        success: true,
        result: summary,
        message: 'Calendar statistics summary retrieved'
      });

    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve calendar statistics',
        error: error.message
      });
    }
  }
);

// GET /api/calendar/stats/workload
// Get technician workload statistics
router.get(
  '/stats/workload',
  requireAuth,
  requirePermission('calendar:view'),
  async (req, res) => {
    try {
      const moment = require('moment');
      const ServiceOrder = require('@/models/appModels/ServiceOrder');

      const startOfWeek = moment().startOf('week').toDate();
      const endOfWeek = moment().endOf('week').toDate();

      const workloadStats = await ServiceOrder.aggregate([
        {
          $match: {
            scheduledDate: { $gte: startOfWeek, $lte: endOfWeek },
            assignedTechnician: { $exists: true }
          }
        },
        {
          $group: {
            _id: '$assignedTechnician',
            totalEvents: { $sum: 1 },
            totalDuration: { $sum: '$estimatedDuration' },
            categories: { $addToSet: '$category' }
          }
        },
        {
          $lookup: {
            from: 'users', // Assuming technicians are in users collection
            localField: '_id',
            foreignField: '_id',
            as: 'technician'
          }
        }
      ]);

      res.status(200).json({
        success: true,
        result: workloadStats,
        message: 'Technician workload statistics retrieved'
      });

    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve workload statistics',
        error: error.message
      });
    }
  }
);

/**
 * HEALTH CHECK ROUTE
 */

// GET /api/calendar/health
// Check calendar system health
router.get(
  '/health',
  requireAuth,
  async (req, res) => {
    try {
      const ServiceOrder = require('@/models/appModels/ServiceOrder');

      const health = {
        status: 'healthy',
        timestamp: new Date(),
        services: {
          database: 'healthy',
          optimization: 'healthy',
          scheduling: 'healthy'
        },
        statistics: {
          totalEvents: await ServiceOrder.countDocuments({}),
          scheduledEvents: await ServiceOrder.countDocuments({ status: 'scheduled' }),
          lastEvent: await ServiceOrder.findOne({})
            .sort({ scheduledDate: -1 })
            .select('scheduledDate')
            .then(event => event?.scheduledDate || null)
        }
      };

      res.status(200).json({
        success: true,
        result: health,
        message: 'Calendar system is healthy'
      });

    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Health check failed',
        error: error.message
      });
    }
  }
);

module.exports = router;
