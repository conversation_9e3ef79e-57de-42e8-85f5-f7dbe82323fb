/**
 * CUSTOMER INTERACTIONS ROUTES
 * API routes for unified customer interactions data panel
 * Combines emails + transcriptions + communication history
 */

const express = require('express');
const { body, param, query } = require('express-validator');

// Import controller
const customerInteractionsController = require('@/controllers/appControllers/customerInteractionsController');

// Import middleware
const { requireAuth } = require('@/middleware/auth');
const { requirePermission } = require('@/middleware/permissions');

const router = express.Router();

/**
 * MAIN CUSTOMER INTERACTIONS ROUTES
 */

// GET /api/customer-interactions/:customerId
// Get comprehensive customer interactions data
router.get(
  '/:customerId',
  requireAuth,
  requirePermission('customer:view'),
  [
    param('customerId').isMongoId().withMessage('Invalid customer ID')
  ],
  customerInteractionsController.getCustomerInteractions
);

// GET /api/customer-interactions/summary/list
// Get customer interactions summary for multiple customers
router.get(
  '/summary/list',
  requireAuth,
  requirePermission('customer:view'),
  [
    query('customerIds')
      .optional()
      .custom((value) => {
        if (typeof value === 'string') {
          const ids = value.split(',');
          return ids.every(id => id.match(/^[0-9a-fA-F]{24}$/));
        }
        return Array.isArray(value) && value.every(id => id.match(/^[0-9a-fA-F]{24}$/));
      })
      .withMessage('Invalid customer IDs format'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100')
  ],
  customerInteractionsController.getCustomerInteractionsSummary
);

// GET /api/customer-interactions/:customerId/timeline
// Get customer interactions timeline with filtering
router.get(
  '/:customerId/timeline',
  requireAuth,
  requirePermission('customer:view'),
  [
    param('customerId').isMongoId().withMessage('Invalid customer ID'),
    query('type')
      .optional()
      .isIn(['all', 'email', 'transcription', 'service', 'sales', 'invoice'])
      .withMessage('Invalid interaction type'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 200 })
      .withMessage('Limit must be between 1 and 200'),
    query('offset')
      .optional()
      .isInt({ min: 0 })
      .withMessage('Offset must be a non-negative integer'),
    query('dateFrom')
      .optional()
      .isISO8601()
      .withMessage('Invalid date format for dateFrom'),
    query('dateTo')
      .optional()
      .isISO8601()
      .withMessage('Invalid date format for dateTo')
  ],
  customerInteractionsController.getCustomerTimeline
);

// GET /api/customer-interactions/:customerId/analytics
// Get customer interaction analytics
router.get(
  '/:customerId/analytics',
  requireAuth,
  requirePermission('customer:view'),
  [
    param('customerId').isMongoId().withMessage('Invalid customer ID')
  ],
  customerInteractionsController.getCustomerAnalytics
);

/**
 * CUSTOMER HEALTH AND INSIGHTS ROUTES
 */

// GET /api/customer-interactions/health/dashboard
// Get customer health dashboard
router.get(
  '/health/dashboard',
  requireAuth,
  requirePermission('customer:view'),
  [
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100')
  ],
  customerInteractionsController.getCustomerHealthDashboard
);

// PUT /api/customer-interactions/:customerId/health-score
// Update customer health score manually
router.put(
  '/:customerId/health-score',
  requireAuth,
  requirePermission('customer:edit'),
  [
    param('customerId').isMongoId().withMessage('Invalid customer ID'),
    body('healthScore')
      .isFloat({ min: 0, max: 100 })
      .withMessage('Health score must be between 0 and 100'),
    body('notes')
      .optional()
      .isString()
      .isLength({ max: 500 })
      .withMessage('Notes must be a string with max 500 characters')
  ],
  customerInteractionsController.updateCustomerHealthScore
);

/**
 * STATISTICS AND REPORTING ROUTES
 */

// GET /api/customer-interactions/stats/overview
// Get customer interactions overview statistics
router.get(
  '/stats/overview',
  requireAuth,
  requirePermission('customer:view'),
  async (req, res) => {
    try {
      const Client = require('@/models/appModels/Client');
      const Email = require('@/models/appModels/Email');
      const ServiceOrder = require('@/models/appModels/ServiceOrder');

      const [
        totalCustomers,
        activeCustomers,
        totalEmails,
        totalServiceOrders,
        recentInteractions
      ] = await Promise.all([
        Client.countDocuments({}),
        Client.countDocuments({ 
          updatedAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
        }),
        Email.countDocuments({}),
        ServiceOrder.countDocuments({}),
        Email.countDocuments({
          receivedAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }
        })
      ]);

      const overview = {
        totalCustomers,
        activeCustomers,
        totalEmails,
        totalServiceOrders,
        recentInteractions,
        activityRate: totalCustomers > 0 ? (activeCustomers / totalCustomers) * 100 : 0,
        avgInteractionsPerCustomer: totalCustomers > 0 ? (totalEmails + totalServiceOrders) / totalCustomers : 0
      };

      res.status(200).json({
        success: true,
        result: overview,
        message: 'Customer interactions overview retrieved'
      });

    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve overview statistics',
        error: error.message
      });
    }
  }
);

// GET /api/customer-interactions/stats/health-distribution
// Get customer health score distribution
router.get(
  '/stats/health-distribution',
  requireAuth,
  requirePermission('customer:view'),
  async (req, res) => {
    try {
      const customerInteractionsService = require('@/services/customerInteractionsService');
      const Client = require('@/models/appModels/Client');

      // Get sample of customers for health analysis
      const customers = await Client.find({}).limit(100).select('_id');
      const customerIds = customers.map(c => c._id.toString());
      
      const summaries = await customerInteractionsService.getCustomerInteractionsSummary(customerIds);

      const distribution = {
        excellent: summaries.filter(c => c.healthScore >= 80).length,
        good: summaries.filter(c => c.healthScore >= 70 && c.healthScore < 80).length,
        fair: summaries.filter(c => c.healthScore >= 50 && c.healthScore < 70).length,
        poor: summaries.filter(c => c.healthScore >= 30 && c.healthScore < 50).length,
        critical: summaries.filter(c => c.healthScore < 30).length
      };

      res.status(200).json({
        success: true,
        result: distribution,
        message: 'Health distribution retrieved'
      });

    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve health distribution',
        error: error.message
      });
    }
  }
);

// GET /api/customer-interactions/stats/interaction-trends
// Get interaction trends over time
router.get(
  '/stats/interaction-trends',
  requireAuth,
  requirePermission('customer:view'),
  async (req, res) => {
    try {
      const Email = require('@/models/appModels/Email');
      const ServiceOrder = require('@/models/appModels/ServiceOrder');

      // Get last 12 weeks of data
      const trends = [];
      for (let i = 11; i >= 0; i--) {
        const weekStart = new Date(Date.now() - (i + 1) * 7 * 24 * 60 * 60 * 1000);
        const weekEnd = new Date(Date.now() - i * 7 * 24 * 60 * 60 * 1000);

        const [emailCount, serviceCount] = await Promise.all([
          Email.countDocuments({
            receivedAt: { $gte: weekStart, $lt: weekEnd }
          }),
          ServiceOrder.countDocuments({
            createdAt: { $gte: weekStart, $lt: weekEnd }
          })
        ]);

        trends.push({
          week: weekStart.toISOString().split('T')[0],
          emails: emailCount,
          services: serviceCount,
          total: emailCount + serviceCount
        });
      }

      res.status(200).json({
        success: true,
        result: trends,
        message: 'Interaction trends retrieved'
      });

    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve interaction trends',
        error: error.message
      });
    }
  }
);

/**
 * HEALTH CHECK ROUTE
 */

// GET /api/customer-interactions/health
// Check customer interactions system health
router.get(
  '/health',
  requireAuth,
  async (req, res) => {
    try {
      const Client = require('@/models/appModels/Client');
      const Email = require('@/models/appModels/Email');

      const health = {
        status: 'healthy',
        timestamp: new Date(),
        services: {
          database: 'healthy',
          ai_insights: 'healthy',
          timeline_aggregation: 'healthy'
        },
        statistics: {
          totalCustomers: await Client.countDocuments({}),
          totalEmails: await Email.countDocuments({}),
          lastInteraction: await Email.findOne({})
            .sort({ receivedAt: -1 })
            .select('receivedAt')
            .then(email => email?.receivedAt || null)
        }
      };

      res.status(200).json({
        success: true,
        result: health,
        message: 'Customer interactions system is healthy'
      });

    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Health check failed',
        error: error.message
      });
    }
  }
);

module.exports = router;
