const express = require('express');
const router = express.Router();

const emailIntelligenceController = require('@/controllers/appControllers/emailIntelligenceController');
const { requireAuth } = require('@/middlewares/requireAuth');

// Apply authentication middleware to all email intelligence routes
router.use(requireAuth);

/**
 * EMAIL INTELLIGENCE DASHBOARD ROUTES
 */

// Get email intelligence insights
router.get('/insights', emailIntelligenceController.getEmailInsights);

// Get system health status
router.get('/health', emailIntelligenceController.getSystemHealth);

// Trigger email processing
router.post('/process-emails', emailIntelligenceController.processEmails);

/**
 * INBOX ZERO METHODOLOGY ROUTES
 */

// Get all emails for Inbox Zero management
router.get('/emails', emailIntelligenceController.getEmails);

// Get Inbox Zero statistics
router.get('/inbox-zero-stats', emailIntelligenceController.getInboxZeroStats);

// Delete emails (4 D's - Delete)
router.post('/emails/delete', emailIntelligenceController.deleteEmails);

// Delegate emails (4 D's - Delegate)
router.post('/emails/delegate', emailIntelligenceController.delegateEmails);

// Defer emails (4 D's - Defer)
router.post('/emails/defer', emailIntelligenceController.deferEmails);

// Do immediate action on email (4 D's - Do)
router.post('/emails/do', emailIntelligenceController.doEmailAction);

/**
 * BULK EMAIL OPERATIONS ROUTES
 */

// Bulk unsubscribe from newsletters
router.post('/emails/bulk-unsubscribe', emailIntelligenceController.bulkUnsubscribe);

// Block senders (cold email protection)
router.post('/emails/block-sender', emailIntelligenceController.blockSender);

// Bulk categorize emails
router.post('/emails/bulk-categorize', emailIntelligenceController.bulkCategorizeEmails);

// Bulk archive emails
router.post('/emails/bulk-archive', emailIntelligenceController.bulkArchiveEmails);

/**
 * EMAIL ANALYTICS ROUTES
 */

// Get email analytics dashboard data
router.get('/analytics', emailIntelligenceController.getEmailAnalytics);

// Get email productivity metrics
router.get('/productivity-metrics', emailIntelligenceController.getProductivityMetrics);

// Get email patterns analysis
router.get('/patterns', emailIntelligenceController.getEmailPatterns);

/**
 * AI-POWERED EMAIL FEATURES ROUTES
 */

// AI email categorization
router.post('/ai/categorize-email/:emailId', emailIntelligenceController.aiCategorizeEmail);

// AI email sentiment analysis
router.post('/ai/sentiment-analysis/:emailId', emailIntelligenceController.aiSentimentAnalysis);

// AI email priority scoring
router.post('/ai/priority-score/:emailId', emailIntelligenceController.aiPriorityScore);

// AI suggested actions for email
router.post('/ai/suggest-actions/:emailId', emailIntelligenceController.aiSuggestActions);

// AI auto-triage (apply 4 D's automatically)
router.post('/ai/auto-triage/:emailId', emailIntelligenceController.aiAutoTriage);

/**
 * EMAIL SEARCH AND FILTERING ROUTES
 */

// Advanced email search
router.post('/search', emailIntelligenceController.searchEmails);

// Filter emails by category
router.get('/filter/:category', emailIntelligenceController.filterEmailsByCategory);

// Get email by ID with full details
router.get('/email/:emailId', emailIntelligenceController.getEmailDetails);

/**
 * HVAC-SPECIFIC EMAIL FEATURES ROUTES
 */

// Process M4A transcription emails
router.post('/process-m4a-emails', emailIntelligenceController.processM4AEmails);

// Extract HVAC service requests from emails
router.post('/extract-service-requests', emailIntelligenceController.extractServiceRequests);

// Generate HVAC quotes from email requests
router.post('/generate-quote-from-email/:emailId', emailIntelligenceController.generateQuoteFromEmail);

// Create service orders from emails
router.post('/create-service-order-from-email/:emailId', emailIntelligenceController.createServiceOrderFromEmail);

/**
 * EMAIL AUTOMATION ROUTES
 */

// Set up email automation rules
router.post('/automation/rules', emailIntelligenceController.createAutomationRule);

// Get automation rules
router.get('/automation/rules', emailIntelligenceController.getAutomationRules);

// Update automation rule
router.put('/automation/rules/:ruleId', emailIntelligenceController.updateAutomationRule);

// Delete automation rule
router.delete('/automation/rules/:ruleId', emailIntelligenceController.deleteAutomationRule);

// Execute automation rule manually
router.post('/automation/execute/:ruleId', emailIntelligenceController.executeAutomationRule);

/**
 * EMAIL TEMPLATES AND RESPONSES ROUTES
 */

// Get email templates
router.get('/templates', emailIntelligenceController.getEmailTemplates);

// Create email template
router.post('/templates', emailIntelligenceController.createEmailTemplate);

// Generate AI response for email
router.post('/ai/generate-response/:emailId', emailIntelligenceController.aiGenerateResponse);

// Send email response
router.post('/send-response/:emailId', emailIntelligenceController.sendEmailResponse);

/**
 * EMAIL INTEGRATION ROUTES
 */

// Sync with external email providers
router.post('/sync/external', emailIntelligenceController.syncExternalEmails);

// Connect email account
router.post('/connect-account', emailIntelligenceController.connectEmailAccount);

// Disconnect email account
router.delete('/disconnect-account/:accountId', emailIntelligenceController.disconnectEmailAccount);

// Get connected accounts
router.get('/connected-accounts', emailIntelligenceController.getConnectedAccounts);

/**
 * EMAIL REPORTING ROUTES
 */

// Generate email intelligence report
router.post('/reports/generate', emailIntelligenceController.generateEmailReport);

// Get email performance dashboard
router.get('/reports/performance', emailIntelligenceController.getEmailPerformanceReport);

// Export email data
router.post('/export', emailIntelligenceController.exportEmailData);

/**
 * REAL-TIME EMAIL MONITORING ROUTES
 */

// Stream real-time email updates
router.get('/stream/updates', emailIntelligenceController.streamEmailUpdates);

// Get live email statistics
router.get('/live-stats', emailIntelligenceController.getLiveEmailStats);

/**
 * EMAIL TESTING AND VALIDATION ROUTES
 */

// Test email intelligence system
router.post('/test/system', emailIntelligenceController.testEmailIntelligenceSystem);

// Validate email processing pipeline
router.post('/test/pipeline', emailIntelligenceController.testEmailPipeline);

// Test AI email features
router.post('/test/ai-features', emailIntelligenceController.testAIEmailFeatures);

module.exports = router;
