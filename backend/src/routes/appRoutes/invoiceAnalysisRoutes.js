/**
 * INVOICE ANALYSIS ROUTES
 * API routes for AI-powered invoice analysis from email attachments
 * Part of HVAC CRM Email Intelligence System
 */

const express = require('express');
const { body, param, query } = require('express-validator');

// Import controller
const invoiceAnalysisController = require('@/controllers/appControllers/invoiceAnalysisController');

// Import middleware
const { requireAuth } = require('@/middleware/auth');
const { requirePermission } = require('@/middleware/permissions');

const router = express.Router();

/**
 * MAIN ANALYSIS ROUTES
 */

// POST /api/invoice-analysis/analyze/:emailId/:attachmentId
// Analyze specific invoice attachment
router.post(
  '/analyze/:emailId/:attachmentId',
  requireAuth,
  requirePermission('invoice:analyze'),
  [
    param('emailId').isMongoId().withMessage('Invalid email ID'),
    param('attachmentId').isMongoId().withMessage('Invalid attachment ID')
  ],
  invoiceAnalysisController.analyzeInvoiceAttachment
);

// POST /api/invoice-analysis/batch-analyze
// Batch analyze all pending invoice attachments
router.post(
  '/batch-analyze',
  requireAuth,
  requirePermission('invoice:batch_analyze'),
  [
    query('daysBack')
      .optional()
      .isInt({ min: 1, max: 365 })
      .withMessage('Days back must be between 1 and 365')
  ],
  invoiceAnalysisController.batchAnalyzeInvoices
);

/**
 * DASHBOARD AND ANALYTICS ROUTES
 */

// GET /api/invoice-analysis/dashboard
// Get invoice analysis dashboard data
router.get(
  '/dashboard',
  requireAuth,
  requirePermission('invoice:view'),
  invoiceAnalysisController.getInvoiceAnalysisDashboard
);

// GET /api/invoice-analysis/invoices
// Get analyzed invoices with filtering
router.get(
  '/invoices',
  requireAuth,
  requirePermission('invoice:view'),
  [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100'),
    query('category')
      .optional()
      .isIn(['all', 'klimatyzacja', 'wentylacja', 'pompa_ciepla', 'serwis', 'inne'])
      .withMessage('Invalid category'),
    query('dateFrom')
      .optional()
      .isISO8601()
      .withMessage('Invalid date format for dateFrom'),
    query('dateTo')
      .optional()
      .isISO8601()
      .withMessage('Invalid date format for dateTo'),
    query('minAmount')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('Minimum amount must be a positive number'),
    query('maxAmount')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('Maximum amount must be a positive number')
  ],
  invoiceAnalysisController.getAnalyzedInvoices
);

// GET /api/invoice-analysis/invoices/:invoiceId
// Get invoice analysis details
router.get(
  '/invoices/:invoiceId',
  requireAuth,
  requirePermission('invoice:view'),
  [
    param('invoiceId').isMongoId().withMessage('Invalid invoice ID')
  ],
  invoiceAnalysisController.getInvoiceAnalysisDetails
);

/**
 * UTILITY ROUTES
 */

// GET /api/invoice-analysis/pending
// Get pending invoice attachments for analysis
router.get(
  '/pending',
  requireAuth,
  requirePermission('invoice:view'),
  [
    query('limit')
      .optional()
      .isInt({ min: 1, max: 200 })
      .withMessage('Limit must be between 1 and 200')
  ],
  invoiceAnalysisController.getPendingInvoiceAttachments
);

// POST /api/invoice-analysis/reanalyze/:invoiceId
// Reanalyze invoice with updated AI
router.post(
  '/reanalyze/:invoiceId',
  requireAuth,
  requirePermission('invoice:analyze'),
  [
    param('invoiceId').isMongoId().withMessage('Invalid invoice ID')
  ],
  invoiceAnalysisController.reanalyzeInvoice
);

/**
 * STATISTICS AND REPORTING ROUTES
 */

// GET /api/invoice-analysis/stats/summary
// Get invoice analysis summary statistics
router.get(
  '/stats/summary',
  requireAuth,
  requirePermission('invoice:view'),
  async (req, res) => {
    try {
      const Email = require('@/models/appModels/Email');
      const Invoice = require('@/models/appModels/Invoice');

      const summary = {
        totalEmailsWithAttachments: await Email.countDocuments({ 'attachments.0': { $exists: true } }),
        totalAnalyzedInvoices: await Email.countDocuments({ 'invoiceAnalysis.analyzed': true }),
        totalInvoiceRecords: await Invoice.countDocuments({ 'analysisData.source_email': { $exists: true } }),
        pendingAnalysis: await Email.countDocuments({
          'attachments.0': { $exists: true },
          'invoiceAnalysis.analyzed': { $ne: true }
        }),
        totalValue: await Invoice.aggregate([
          { $match: { 'analysisData.source_email': { $exists: true } } },
          { $group: { _id: null, total: { $sum: '$total' } } }
        ]).then(result => result[0]?.total || 0),
        averageInvoiceValue: await Invoice.aggregate([
          { $match: { 'analysisData.source_email': { $exists: true } } },
          { $group: { _id: null, avg: { $avg: '$total' } } }
        ]).then(result => result[0]?.avg || 0)
      };

      res.status(200).json({
        success: true,
        result: summary,
        message: 'Invoice analysis summary retrieved'
      });

    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve summary statistics',
        error: error.message
      });
    }
  }
);

// GET /api/invoice-analysis/stats/categories
// Get invoice analysis by HVAC categories
router.get(
  '/stats/categories',
  requireAuth,
  requirePermission('invoice:view'),
  async (req, res) => {
    try {
      const Invoice = require('@/models/appModels/Invoice');

      const categoryStats = await Invoice.aggregate([
        { $match: { 'analysisData.source_email': { $exists: true } } },
        {
          $group: {
            _id: '$hvacCategory',
            count: { $sum: 1 },
            totalValue: { $sum: '$total' },
            averageValue: { $avg: '$total' },
            averageConfidence: { $avg: '$analysisData.confidence_score' }
          }
        },
        { $sort: { totalValue: -1 } }
      ]);

      res.status(200).json({
        success: true,
        result: categoryStats,
        message: 'Category statistics retrieved'
      });

    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve category statistics',
        error: error.message
      });
    }
  }
);

// GET /api/invoice-analysis/stats/monthly
// Get monthly invoice analysis trends
router.get(
  '/stats/monthly',
  requireAuth,
  requirePermission('invoice:view'),
  async (req, res) => {
    try {
      const Invoice = require('@/models/appModels/Invoice');

      const monthlyStats = await Invoice.aggregate([
        { $match: { 'analysisData.source_email': { $exists: true } } },
        {
          $group: {
            _id: {
              year: { $year: '$date' },
              month: { $month: '$date' }
            },
            count: { $sum: 1 },
            totalValue: { $sum: '$total' },
            averageValue: { $avg: '$total' },
            categories: { $addToSet: '$hvacCategory' }
          }
        },
        { $sort: { '_id.year': -1, '_id.month': -1 } },
        { $limit: 12 }
      ]);

      res.status(200).json({
        success: true,
        result: monthlyStats,
        message: 'Monthly statistics retrieved'
      });

    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve monthly statistics',
        error: error.message
      });
    }
  }
);

/**
 * HEALTH CHECK ROUTE
 */

// GET /api/invoice-analysis/health
// Check invoice analysis system health
router.get(
  '/health',
  requireAuth,
  async (req, res) => {
    try {
      const health = {
        status: 'healthy',
        timestamp: new Date(),
        services: {
          ocr: 'healthy',
          ai: 'healthy',
          database: 'healthy'
        },
        lastAnalysis: await require('@/models/appModels/Email')
          .findOne({ 'invoiceAnalysis.analyzed': true })
          .sort({ 'invoiceAnalysis.analyzedAt': -1 })
          .select('invoiceAnalysis.analyzedAt')
          .then(email => email?.invoiceAnalysis?.analyzedAt || null)
      };

      res.status(200).json({
        success: true,
        result: health,
        message: 'Invoice analysis system is healthy'
      });

    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Health check failed',
        error: error.message
      });
    }
  }
);

module.exports = router;
