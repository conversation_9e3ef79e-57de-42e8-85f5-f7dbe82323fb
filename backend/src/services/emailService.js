const nodemailer = require('nodemailer');
const Imap = require('imap');
const { simpleParser } = require('mailparser');
const Email = require('@/models/appModels/Email');
const aiService = require('./aiService');
const enhancedAIService = require('./enhancedAIService');

/**
 * Email Service for HVAC CRM with Inbox Zero methodology
 * Handles email processing, categorization, and automation
 */

class EmailService {
  constructor() {
    this.imapConfig = {
      host: process.env.IMAP_HOST || 'imap.gmail.com',
      port: process.env.IMAP_PORT || 993,
      secure: true,
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASSWORD
      }
    };

    this.smtpConfig = {
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: process.env.SMTP_PORT || 587,
      secure: false,
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASSWORD
      }
    };

    this.transporter = nodemailer.createTransporter(this.smtpConfig);
  }

  /**
   * Process incoming emails from configured accounts
   */
  async processIncomingEmails() {
    try {
      console.log('🔄 Starting email processing...');
      
      const client = new ImapFlow(this.imapConfig);
      await client.connect();
      
      // Select INBOX
      let lock = await client.getMailboxLock('INBOX');
      
      try {
        // Get unread emails
        const messages = client.fetch('1:*', { 
          envelope: true, 
          bodyStructure: true,
          source: true 
        }, { uid: true });
        
        let processedCount = 0;
        
        for await (let message of messages) {
          try {
            const parsed = await simpleParser(message.source);
            const emailData = await this.parseEmailMessage(parsed, message);
            
            // Check if email already exists
            const existingEmail = await Email.findOne({ messageId: emailData.messageId });
            if (existingEmail) {
              continue;
            }
            
            // Save email to database
            const savedEmail = await Email.create(emailData);
            
            // Process with AI
            await this.processEmailWithAI(savedEmail);
            
            processedCount++;
            console.log(`✅ Processed email: ${emailData.subject}`);
            
          } catch (error) {
            console.error('❌ Error processing individual email:', error);
          }
        }
        
        console.log(`🎉 Email processing completed. Processed ${processedCount} emails.`);
        return { processedCount, success: true };
        
      } finally {
        lock.release();
        await client.logout();
      }
      
    } catch (error) {
      console.error('❌ Error in email processing:', error);
      throw error;
    }
  }

  /**
   * Parse email message into our data structure
   */
  async parseEmailMessage(parsed, message) {
    const emailData = {
      messageId: parsed.messageId || `msg_${Date.now()}_${Math.random()}`,
      subject: parsed.subject || 'No Subject',
      content: parsed.text || '',
      htmlContent: parsed.html || '',
      
      sender: parsed.from?.text || 'Unknown Sender',
      senderEmail: parsed.from?.value?.[0]?.address || '<EMAIL>',
      
      recipients: this.parseRecipients(parsed),
      
      receivedAt: parsed.date || new Date(),
      sentAt: parsed.date || new Date(),
      
      attachments: await this.parseAttachments(parsed.attachments || []),
      
      // Extract email metadata
      threadId: parsed.inReplyTo || parsed.messageId,
      inReplyTo: parsed.inReplyTo,
      references: parsed.references || [],
      
      // Initialize with default values
      read: false,
      category: null,
      priority: 'medium',
      source: 'imap',
      
      // Extract links
      unsubscribeLink: this.extractUnsubscribeLink(parsed.html || parsed.text || ''),
      externalLinks: this.extractExternalLinks(parsed.html || parsed.text || ''),
      
      enabled: true
    };

    return emailData;
  }

  /**
   * Parse email recipients
   */
  parseRecipients(parsed) {
    const recipients = [];
    
    if (parsed.to) {
      parsed.to.value.forEach(addr => {
        recipients.push({
          email: addr.address,
          name: addr.name,
          type: 'to'
        });
      });
    }
    
    if (parsed.cc) {
      parsed.cc.value.forEach(addr => {
        recipients.push({
          email: addr.address,
          name: addr.name,
          type: 'cc'
        });
      });
    }
    
    if (parsed.bcc) {
      parsed.bcc.value.forEach(addr => {
        recipients.push({
          email: addr.address,
          name: addr.name,
          type: 'bcc'
        });
      });
    }
    
    return recipients;
  }

  /**
   * Parse email attachments
   */
  async parseAttachments(attachments) {
    const parsedAttachments = [];
    
    for (const attachment of attachments) {
      const attachmentData = {
        filename: attachment.filename || 'unknown',
        contentType: attachment.contentType || 'application/octet-stream',
        size: attachment.size || 0,
        isM4A: attachment.contentType?.includes('audio') || attachment.filename?.endsWith('.m4a'),
        transcribed: false
      };
      
      // If it's an M4A file, mark for transcription
      if (attachmentData.isM4A) {
        attachmentData.transcriptionText = '';
        attachmentData.transcriptionConfidence = 0;
      }
      
      parsedAttachments.push(attachmentData);
    }
    
    return parsedAttachments;
  }

  /**
   * Extract unsubscribe link from email content
   */
  extractUnsubscribeLink(content) {
    const unsubscribeRegex = /https?:\/\/[^\s]+unsubscribe[^\s]*/gi;
    const matches = content.match(unsubscribeRegex);
    return matches ? matches[0] : null;
  }

  /**
   * Extract external links from email content
   */
  extractExternalLinks(content) {
    const linkRegex = /https?:\/\/[^\s<>"]+/gi;
    const matches = content.match(linkRegex) || [];
    return matches.slice(0, 10); // Limit to first 10 links
  }

  /**
   * Process email with AI for categorization and analysis
   */
  async processEmailWithAI(email) {
    try {
      console.log(`🤖 Processing email with AI: ${email.subject}`);
      
      // AI Categorization
      const category = await this.categorizeEmailWithAI(email);
      email.category = category;
      email.categorizedAt = new Date();
      
      // AI Sentiment Analysis
      const sentiment = await this.analyzeSentiment(email);
      email.aiAnalysis = {
        ...email.aiAnalysis,
        sentiment: sentiment.sentiment,
        sentimentScore: sentiment.score
      };
      
      // HVAC-specific analysis
      const hvacAnalysis = await this.analyzeHVACContent(email);
      email.hvacData = hvacAnalysis;
      
      // Priority scoring
      const priority = await this.calculatePriority(email);
      email.priority = priority;
      
      // Mark as processed
      email.processedAt = new Date();
      
      await email.save();
      
      console.log(`✅ AI processing completed for: ${email.subject}`);
      
    } catch (error) {
      console.error('❌ Error in AI processing:', error);
    }
  }

  /**
   * Categorize email using AI
   */
  async categorizeEmailWithAI(email) {
    try {
      const prompt = `
        Categorize this email into one of these categories:
        - customer: Customer service requests, complaints, inquiries
        - newsletter: Marketing emails, newsletters, promotions
        - cold: Unsolicited sales emails, cold outreach
        - internal: Internal company communications
        - urgent: Urgent requests requiring immediate attention
        - service: HVAC service requests, maintenance, repairs
        - quote: Quote requests, pricing inquiries
        - invoice: Billing, invoices, payment related
        
        Email Subject: ${email.subject}
        Email Content: ${email.content.substring(0, 500)}
        Sender: ${email.senderEmail}
        
        Return only the category name.
      `;
      
      const response = await aiService.generateResponse(prompt);
      const category = response.toLowerCase().trim();
      
      const validCategories = ['customer', 'newsletter', 'cold', 'internal', 'urgent', 'service', 'quote', 'invoice'];
      return validCategories.includes(category) ? category : 'customer';
      
    } catch (error) {
      console.error('Error in AI categorization:', error);
      return 'customer'; // Default category
    }
  }

  /**
   * Analyze email sentiment
   */
  async analyzeSentiment(email) {
    try {
      const prompt = `
        Analyze the sentiment of this email and return a JSON object with:
        - sentiment: "positive", "negative", or "neutral"
        - score: number between -1 (very negative) and 1 (very positive)
        
        Email: ${email.content.substring(0, 300)}
      `;
      
      const response = await aiService.generateResponse(prompt);
      return JSON.parse(response);
      
    } catch (error) {
      console.error('Error in sentiment analysis:', error);
      return { sentiment: 'neutral', score: 0 };
    }
  }

  /**
   * Analyze HVAC-specific content
   */
  async analyzeHVACContent(email) {
    try {
      const content = `${email.subject} ${email.content}`.toLowerCase();
      
      const hvacKeywords = [
        'air conditioning', 'hvac', 'heating', 'cooling', 'ventilation',
        'furnace', 'boiler', 'heat pump', 'ductwork', 'thermostat',
        'installation', 'maintenance', 'repair', 'service', 'inspection'
      ];
      
      const serviceTypes = {
        installation: ['install', 'installation', 'new system', 'replace'],
        maintenance: ['maintenance', 'service', 'check', 'tune-up'],
        repair: ['repair', 'fix', 'broken', 'not working', 'problem'],
        inspection: ['inspection', 'check', 'evaluate', 'assessment'],
        quote: ['quote', 'estimate', 'price', 'cost', 'how much']
      };
      
      const isServiceRelated = hvacKeywords.some(keyword => content.includes(keyword));
      
      let serviceType = null;
      for (const [type, keywords] of Object.entries(serviceTypes)) {
        if (keywords.some(keyword => content.includes(keyword))) {
          serviceType = type;
          break;
        }
      }
      
      const customerType = content.includes('commercial') || content.includes('business') ? 'commercial' :
                          content.includes('industrial') ? 'industrial' : 'residential';
      
      return {
        isServiceRelated,
        serviceType,
        customerType,
        equipmentMentioned: hvacKeywords.filter(keyword => content.includes(keyword)),
        location: this.extractLocation(content),
        estimatedValue: this.estimateValue(content)
      };
      
    } catch (error) {
      console.error('Error in HVAC analysis:', error);
      return { isServiceRelated: false };
    }
  }

  /**
   * Calculate email priority
   */
  async calculatePriority(email) {
    const urgentKeywords = ['urgent', 'emergency', 'asap', 'immediately', 'critical'];
    const highKeywords = ['important', 'priority', 'soon', 'quickly'];
    
    const content = `${email.subject} ${email.content}`.toLowerCase();
    
    if (urgentKeywords.some(keyword => content.includes(keyword))) {
      return 'urgent';
    } else if (highKeywords.some(keyword => content.includes(keyword))) {
      return 'high';
    } else if (email.category === 'service' || email.category === 'customer') {
      return 'medium';
    } else {
      return 'low';
    }
  }

  /**
   * Extract location from email content
   */
  extractLocation(content) {
    // Simple location extraction - could be enhanced with NLP
    const locationRegex = /\b\d{1,5}\s+[\w\s]+(?:street|st|avenue|ave|road|rd|drive|dr|lane|ln|boulevard|blvd)\b/gi;
    const matches = content.match(locationRegex);
    return matches ? matches[0] : null;
  }

  /**
   * Estimate value from email content
   */
  estimateValue(content) {
    const priceRegex = /\$[\d,]+/g;
    const matches = content.match(priceRegex);
    if (matches) {
      const prices = matches.map(price => parseInt(price.replace(/[$,]/g, '')));
      return Math.max(...prices);
    }
    return null;
  }

  /**
   * Send email response
   */
  async sendEmail(to, subject, content, options = {}) {
    try {
      const mailOptions = {
        from: process.env.EMAIL_USER,
        to,
        subject,
        text: content,
        html: options.html || content,
        ...options
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log('✅ Email sent successfully:', result.messageId);
      return result;
      
    } catch (error) {
      console.error('❌ Error sending email:', error);
      throw error;
    }
  }
}

module.exports = new EmailService();
